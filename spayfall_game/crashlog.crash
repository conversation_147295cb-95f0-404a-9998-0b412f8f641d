Incident Identifier: FBF16E77-E968-432B-9BF6-9A68421A1DD8
Distributor ID:      com.apple.TestFlight
Hardware Model:      iPhone17,2
Process:             Runner [82178]
Path:                /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Runner
Identifier:          com.spayfall.spayfallGame
Version:             1.0.0 (1)
AppStoreTools:       16F3
AppVariant:          1:iPhone17,2:18
Beta:                YES
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.spayfall.spayfallGame [11444]

Date/Time:           2025-06-24 13:47:50.2174 +0300
Launch Time:         2025-06-24 13:47:50.0475 +0300
OS Version:          iPhone OS 18.5 (22F76)
Release Type:        User
Baseband Version:    1.60.02
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Termination Reason: SIGNAL 6 Abort trap: 6
Terminating Process: Runner [82178]

Triggered by Thread:  0

Last Exception Backtrace:
0   CoreFoundation                	0x19d38b21c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x19a825abc objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreFoundation                	0x19d3e9ea0 +[NSException raise:format:] + 128 (NSException.m:0)
3   FirebaseCore                  	0x100d04dd4 +[FIRApp addAppToAppDictionary:] + 168 (FIRApp.m:307)
4   FirebaseCore                  	0x100d04628 +[FIRApp configureWithName:options:] + 652 (FIRApp.m:187)
5   Runner                        	0x100b6b67c -[FLTFirebaseCorePlugin initializeAppAppName:initializeAppRequest:completion:] + 1156 (FLTFirebaseCorePlugin.m:190)
6   Runner                        	0x100b6da4c __FirebaseCoreHostApiSetup_block_invoke + 192 (messages.g.m:231)
7   Flutter                       	0x103069fe8 __48-[FlutterBasicMessageChannel setMessageHandler:]_block_invoke + 160 (FlutterChannels.mm:139)
8   Flutter                       	0x102bfa808 invocation function for block in flutter::PlatformMessageHandlerIos::HandlePlatformMessage(std::_fl::unique_ptr<flutter::PlatformMessage, std::_fl::default_delete<flutter::PlatformMessage>>) + 116 (platform_message_handler_ios.mm:70)
9   libdispatch.dylib             	0x1a51fcaac _dispatch_call_block_and_release + 32 (init.c:1575)
10  libdispatch.dylib             	0x1a5216584 _dispatch_client_callout + 16 (client_callout.mm:85)
11  libdispatch.dylib             	0x1a52335a0 _dispatch_main_queue_drain.cold.5 + 812 (queue.c:8104)
12  libdispatch.dylib             	0x1a520bd30 _dispatch_main_queue_drain + 180 (queue.c:8085)
13  libdispatch.dylib             	0x1a520bc6c _dispatch_main_queue_callback_4CF + 44 (queue.c:8264)
14  CoreFoundation                	0x19d2ddd90 __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 16 (CFRunLoop.c:1793)
15  CoreFoundation                	0x19d2814f4 __CFRunLoopRun + 1980 (CFRunLoop.c:3163)
16  CoreFoundation                	0x19d282c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
17  GraphicsServices              	0x1ea461454 GSEventRunModal + 168 (GSEvent.c:2196)
18  UIKitCore                     	0x19fc95274 -[UIApplication _run] + 816 (UIApplication.m:3845)
19  UIKitCore                     	0x19fc60a28 UIApplicationMain + 336 (UIApplication.m:5540)
20  UIKitCore                     	0x19fd42168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
21  Runner                        	0x100a883a8 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ6Runner03AppB0C_Ttg5 + 28 (/<compiler-generated>:4)
22  Runner                        	0x100a883a8 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
23  Runner                        	0x100a883a8 main + 120
24  dyld                          	0x1c4157f08 start + 6040 (dyldMain.cpp:1450)

Thread 0 name:
Thread 0 Crashed:
0   libsystem_kernel.dylib        	0x00000001ee49a1dc __pthread_kill + 8
1   libsystem_pthread.dylib       	0x000000022799fc60 pthread_kill + 268 (pthread.c:1721)
2   libsystem_c.dylib             	0x00000001a52b82d0 abort + 124 (abort.c:122)
3   libc++abi.dylib               	0x00000002278c95a0 abort_message + 132 (abort_message.cpp:78)
4   libc++abi.dylib               	0x00000002278b7f10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
5   libobjc.A.dylib               	0x000000019a827bf8 _objc_terminate() + 156 (objc-exception.mm:496)
6   libc++abi.dylib               	0x00000002278c88b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000002278cbe1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x00000002278cbdc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x000000019a825c24 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreFoundation                	0x000000019d3e9ea0 +[NSException raise:format:] + 128 (NSException.m:0)
11  FirebaseCore                  	0x0000000100d04dd4 +[FIRApp addAppToAppDictionary:] + 168 (FIRApp.m:307)
12  FirebaseCore                  	0x0000000100d04628 +[FIRApp configureWithName:options:] + 652 (FIRApp.m:187)
13  Runner                        	0x0000000100b6b67c -[FLTFirebaseCorePlugin initializeAppAppName:initializeAppRequest:completion:] + 1156 (FLTFirebaseCorePlugin.m:190)
14  Runner                        	0x0000000100b6da4c __FirebaseCoreHostApiSetup_block_invoke + 192 (messages.g.m:231)
15  Flutter                       	0x0000000103069fe8 __48-[FlutterBasicMessageChannel setMessageHandler:]_block_invoke + 160 (FlutterChannels.mm:139)
16  Flutter                       	0x0000000102bfa808 invocation function for block in flutter::PlatformMessageHandlerIos::HandlePlatformMessage(std::_fl::unique_ptr<flutter::PlatformMessage, std::_fl::default_delete<flutter::PlatformMessage>>) + 116 (platform_message_handler_ios.mm:70)
17  libdispatch.dylib             	0x00000001a51fcaac _dispatch_call_block_and_release + 32 (init.c:1575)
18  libdispatch.dylib             	0x00000001a5216584 _dispatch_client_callout + 16 (client_callout.mm:85)
19  libdispatch.dylib             	0x00000001a52335a0 _dispatch_main_queue_drain.cold.5 + 812 (queue.c:8104)
20  libdispatch.dylib             	0x00000001a520bd30 _dispatch_main_queue_drain + 180 (queue.c:8085)
21  libdispatch.dylib             	0x00000001a520bc6c _dispatch_main_queue_callback_4CF + 44 (queue.c:8264)
22  CoreFoundation                	0x000000019d2ddd90 __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 16 (CFRunLoop.c:1793)
23  CoreFoundation                	0x000000019d2814f4 __CFRunLoopRun + 1980 (CFRunLoop.c:3163)
24  CoreFoundation                	0x000000019d282c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
25  GraphicsServices              	0x00000001ea461454 GSEventRunModal + 168 (GSEvent.c:2196)
26  UIKitCore                     	0x000000019fc95274 -[UIApplication _run] + 816 (UIApplication.m:3845)
27  UIKitCore                     	0x000000019fc60a28 UIApplicationMain + 336 (UIApplication.m:5540)
28  UIKitCore                     	0x000000019fd42168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
29  Runner                        	0x0000000100a883a8 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ6Runner03AppB0C_Ttg5 + 28 (/<compiler-generated>:4)
30  Runner                        	0x0000000100a883a8 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
31  Runner                        	0x0000000100a883a8 main + 120
32  dyld                          	0x00000001c4157f08 start + 6040 (dyldMain.cpp:1450)

Thread 1:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 2:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 3:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 4:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 5:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001ee48fc60 semaphore_wait_trap + 8
1   libdispatch.dylib             	0x00000001a51fe8e0 _dispatch_sema4_wait + 28 (lock.c:139)
2   libdispatch.dylib             	0x00000001a51fee90 _dispatch_semaphore_wait_slow + 132 (semaphore.c:132)
3   Metal                         	0x000000019ce01008 XPCCompilerConnection::BuildRequestInternal(MTLCompilerRequest*, char const*, NSObject<OS_dispatch_data>*, int, bool, void (unsigned int, void const*, unsigned long, char const*) block_pointer) + 136 (MTLCompilerConnection.mm:1634)
4   libdispatch.dylib             	0x00000001a51fcaac _dispatch_call_block_and_release + 32 (init.c:1575)
5   libdispatch.dylib             	0x00000001a5216584 _dispatch_client_callout + 16 (client_callout.mm:85)
6   libdispatch.dylib             	0x00000001a52052d0 _dispatch_lane_serial_drain + 740 (queue.c:3939)
7   libdispatch.dylib             	0x00000001a5205de0 _dispatch_lane_invoke + 440 (queue.c:4030)
8   libdispatch.dylib             	0x00000001a52101dc _dispatch_root_queue_drain_deferred_wlh + 292 (queue.c:7198)
9   libdispatch.dylib             	0x00000001a520fa60 _dispatch_workloop_worker_thread + 540 (queue.c:6792)
10  libsystem_pthread.dylib       	0x0000000227998a0c _pthread_wqthread + 292 (pthread.c:2696)
11  libsystem_pthread.dylib       	0x0000000227998aac start_wqthread + 8

Thread 7:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001ee48fce4 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	0x00000001ee49339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ee4932b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ee493100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019d282900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019d2811f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019d282c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x000000019befa79c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x000000019bf00020 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x000000019fc7f56c -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x000000019bf60804 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000022799b344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x0000000227998ab8 thread_start + 8

Thread 9:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001ee48fc60 semaphore_wait_trap + 8
1   libdispatch.dylib             	0x00000001a51fe8e0 _dispatch_sema4_wait + 28 (lock.c:139)
2   libdispatch.dylib             	0x00000001a51fee90 _dispatch_semaphore_wait_slow + 132 (semaphore.c:132)
3   Metal                         	0x000000019ce01008 XPCCompilerConnection::BuildRequestInternal(MTLCompilerRequest*, char const*, NSObject<OS_dispatch_data>*, int, bool, void (unsigned int, void const*, unsigned long, char const*) block_pointer) + 136 (MTLCompilerConnection.mm:1634)
4   libdispatch.dylib             	0x00000001a51fcaac _dispatch_call_block_and_release + 32 (init.c:1575)
5   libdispatch.dylib             	0x00000001a5216584 _dispatch_client_callout + 16 (client_callout.mm:85)
6   libdispatch.dylib             	0x00000001a52052d0 _dispatch_lane_serial_drain + 740 (queue.c:3939)
7   libdispatch.dylib             	0x00000001a5205de0 _dispatch_lane_invoke + 440 (queue.c:4030)
8   libdispatch.dylib             	0x00000001a52101dc _dispatch_root_queue_drain_deferred_wlh + 292 (queue.c:7198)
9   libdispatch.dylib             	0x00000001a520fa60 _dispatch_workloop_worker_thread + 540 (queue.c:6792)
10  libsystem_pthread.dylib       	0x0000000227998a0c _pthread_wqthread + 292 (pthread.c:2696)
11  libsystem_pthread.dylib       	0x0000000227998aac start_wqthread + 8

Thread 11:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 12:
0   libsystem_pthread.dylib       	0x0000000227998aa4 start_wqthread + 0

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001ee48fce4 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	0x00000001ee49339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ee4932b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ee493100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019d282900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019d2811f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019d282c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Flutter                       	0x0000000102c24104 fml::MessageLoopDarwin::Run() + 88 (message_loop_darwin.mm:51)
8   Flutter                       	0x0000000102c23dec fml::MessageLoopImpl::DoRun() + 28 (message_loop_impl.cc:94)
9   Flutter                       	0x0000000102c23dec fml::MessageLoop::Run() + 32 (message_loop.cc:49)
10  Flutter                       	0x0000000102c23dec fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0::operator()() const + 160 (thread.cc:154)
11  Flutter                       	0x0000000102c23dec decltype(std::declval<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0&>()()) std::_fl::__invoke[abi:nn210000]<fml::Thr... + 160 (invoke.h:179)
12  Flutter                       	0x0000000102c23dec void std::_fl::__invoke_void_return_wrapper<void, true>::__call[abi:nn210000]<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const... + 160 (invoke.h:251)
13  Flutter                       	0x0000000102c23dec void std::_fl::__invoke_r[abi:nn210000]<void, fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0&>(fml::Thread::Thread(st... + 160 (invoke.h:273)
14  Flutter                       	0x0000000102c23dec std::_fl::__function::__alloc_func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::T... + 160 (function.h:167)
15  Flutter                       	0x0000000102c23dec std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(... + 180 (function.h:319)
16  Flutter                       	0x0000000102c23afc std::_fl::__function::__value_func<void ()>::operator()[abi:nn210000]() const + 20 (function.h:436)
17  Flutter                       	0x0000000102c23afc std::_fl::function<void ()>::operator()() const + 20 (function.h:995)
18  Flutter                       	0x0000000102c23afc fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::operator()(void*) const + 20 (thread.cc:76)
19  Flutter                       	0x0000000102c23afc fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36 (thread.cc:73)
20  libsystem_pthread.dylib       	0x000000022799b344 _pthread_start + 136 (pthread.c:931)
21  libsystem_pthread.dylib       	0x0000000227998ab8 thread_start + 8

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001ee48fce4 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	0x00000001ee49339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ee4932b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ee493100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019d282900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019d2811f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019d282c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Flutter                       	0x0000000102c24104 fml::MessageLoopDarwin::Run() + 88 (message_loop_darwin.mm:51)
8   Flutter                       	0x0000000102c23dec fml::MessageLoopImpl::DoRun() + 28 (message_loop_impl.cc:94)
9   Flutter                       	0x0000000102c23dec fml::MessageLoop::Run() + 32 (message_loop.cc:49)
10  Flutter                       	0x0000000102c23dec fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0::operator()() const + 160 (thread.cc:154)
11  Flutter                       	0x0000000102c23dec decltype(std::declval<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0&>()()) std::_fl::__invoke[abi:nn210000]<fml::Thr... + 160 (invoke.h:179)
12  Flutter                       	0x0000000102c23dec void std::_fl::__invoke_void_return_wrapper<void, true>::__call[abi:nn210000]<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const... + 160 (invoke.h:251)
13  Flutter                       	0x0000000102c23dec void std::_fl::__invoke_r[abi:nn210000]<void, fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0&>(fml::Thread::Thread(st... + 160 (invoke.h:273)
14  Flutter                       	0x0000000102c23dec std::_fl::__function::__alloc_func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::T... + 160 (function.h:167)
15  Flutter                       	0x0000000102c23dec std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(... + 180 (function.h:319)
16  Flutter                       	0x0000000102c23afc std::_fl::__function::__value_func<void ()>::operator()[abi:nn210000]() const + 20 (function.h:436)
17  Flutter                       	0x0000000102c23afc std::_fl::function<void ()>::operator()() const + 20 (function.h:995)
18  Flutter                       	0x0000000102c23afc fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::operator()(void*) const + 20 (thread.cc:76)
19  Flutter                       	0x0000000102c23afc fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36 (thread.cc:73)
20  libsystem_pthread.dylib       	0x000000022799b344 _pthread_start + 136 (pthread.c:931)
21  libsystem_pthread.dylib       	0x0000000227998ab8 thread_start + 8

Thread 15 name:
Thread 15:
0   libsystem_kernel.dylib        	0x00000001ee495438 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	0x0000000227999e50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   Flutter                       	0x0000000102bff3b4 std::_fl::__libcpp_condvar_wait[abi:nn210000](_opaque_pthread_cond_t*, _opaque_pthread_mutex_t*) + 4 (pthread.h:122)
3   Flutter                       	0x0000000102bff3b4 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 20 (condition_variable.cpp:30)
4   Flutter                       	0x0000000102c1d488 void std::_fl::condition_variable::wait<fml::ConcurrentMessageLoop::WorkerMain()::$_0>(std::_fl::unique_lock<std::_fl::mutex>&, fml::ConcurrentMessageLoop::WorkerMain()::$_0) + 44 (condition_variable.h:147)
5   Flutter                       	0x0000000102c1d488 fml::ConcurrentMessageLoop::WorkerMain() + 60 (concurrent_message_loop.cc:75)
6   Flutter                       	0x0000000102c1d488 fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0::operator()() const + 292 (concurrent_message_loop.cc:20)
7   Flutter                       	0x0000000102c1d488 decltype(std::declval<fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>()()) std::_fl::__invoke[abi:nn210000]<fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::... + 292 (invoke.h:179)
8   Flutter                       	0x0000000102c1d488 void std::_fl::__thread_execute[abi:nn210000]<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop... + 292 (thread.h:200)
9   Flutter                       	0x0000000102c1d488 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::Concur... + 392 (thread.h:209)
10  libsystem_pthread.dylib       	0x000000022799b344 _pthread_start + 136 (pthread.c:931)
11  libsystem_pthread.dylib       	0x0000000227998ab8 thread_start + 8

Thread 16 name:
Thread 16:
0   libsystem_kernel.dylib        	0x00000001ee495438 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	0x0000000227999e50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   Flutter                       	0x0000000102bff3b4 std::_fl::__libcpp_condvar_wait[abi:nn210000](_opaque_pthread_cond_t*, _opaque_pthread_mutex_t*) + 4 (pthread.h:122)
3   Flutter                       	0x0000000102bff3b4 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 20 (condition_variable.cpp:30)
4   Flutter                       	0x0000000102c1d488 void std::_fl::condition_variable::wait<fml::ConcurrentMessageLoop::WorkerMain()::$_0>(std::_fl::unique_lock<std::_fl::mutex>&, fml::ConcurrentMessageLoop::WorkerMain()::$_0) + 44 (condition_variable.h:147)
5   Flutter                       	0x0000000102c1d488 fml::ConcurrentMessageLoop::WorkerMain() + 60 (concurrent_message_loop.cc:75)
6   Flutter                       	0x0000000102c1d488 fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0::operator()() const + 292 (concurrent_message_loop.cc:20)
7   Flutter                       	0x0000000102c1d488 decltype(std::declval<fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>()()) std::_fl::__invoke[abi:nn210000]<fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::... + 292 (invoke.h:179)
8   Flutter                       	0x0000000102c1d488 void std::_fl::__thread_execute[abi:nn210000]<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop... + 292 (thread.h:200)
9   Flutter                       	0x0000000102c1d488 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::Concur... + 392 (thread.h:209)
10  libsystem_pthread.dylib       	0x000000022799b344 _pthread_start + 136 (pthread.c:931)
11  libsystem_pthread.dylib       	0x0000000227998ab8 thread_start + 8

Thread 17 name:
Thread 17:
0   libsystem_kernel.dylib        	0x00000001ee495438 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	0x0000000227999e50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   Flutter                       	0x0000000102bff3b4 std::_fl::__libcpp_condvar_wait[abi:nn210000](_opaque_pthread_cond_t*, _opaque_pthread_mutex_t*) + 4 (pthread.h:122)
3   Flutter                       	0x0000000102bff3b4 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 20 (condition_variable.cpp:30)
4   Flutter                       	0x0000000102c1d488 void std::_fl::condition_variable::wait<fml::ConcurrentMessageLoop::WorkerMain()::$_0>(std::_fl::unique_lock<std::_fl::mutex>&, fml::ConcurrentMessageLoop::WorkerMain()::$_0) + 44 (condition_variable.h:147)
5   Flutter                       	0x0000000102c1d488 fml::ConcurrentMessageLoop::WorkerMain() + 60 (concurrent_message_loop.cc:75)
6   Flutter                       	0x0000000102c1d488 fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0::operator()() const + 292 (concurrent_message_loop.cc:20)
7   Flutter                       	0x0000000102c1d488 decltype(std::declval<fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>()()) std::_fl::__invoke[abi:nn210000]<fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::... + 292 (invoke.h:179)
8   Flutter                       	0x0000000102c1d488 void std::_fl::__thread_execute[abi:nn210000]<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop... + 292 (thread.h:200)
9   Flutter                       	0x0000000102c1d488 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::Concur... + 392 (thread.h:209)
10  libsystem_pthread.dylib       	0x000000022799b344 _pthread_start + 136 (pthread.c:931)
11  libsystem_pthread.dylib       	0x0000000227998ab8 thread_start + 8

Thread 18 name:
Thread 18:
0   libsystem_kernel.dylib        	0x00000001ee496768 kevent + 8
1   Flutter                       	0x0000000103016c7c dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long) + 364 (eventhandler_macos.cc:459)
2   Flutter                       	0x0000000103042fcc dart::bin::ThreadStart(void*) + 88 (thread_macos.cc:65)
3   libsystem_pthread.dylib       	0x000000022799b344 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x0000000227998ab8 thread_start + 8


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x00000002278cdfdb   x5: 0x000000016f378fe0   x6: 0x000000000000006e   x7: 0x11f548ab05e19fc3
    x8: 0x1cf6701aeb90396e   x9: 0x1cf67018ec6bfc2e  x10: 0x0000000000000051  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x000000019d72c494  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000148  x17: 0x0000000207fbc540  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x0000000000000103  x21: 0x0000000207fbc620  x22: 0x0000000205ad8000  x23: 0x0000000100c11000
   x24: 0x0000000100d18000  x25: 0x000000010a4e4a50  x26: 0x0000000000000000  x27: 0x0000000209fc45b0
   x28: 0x0000000000000000   fp: 0x000000016f378f50   lr: 0x000000022799fc60
    sp: 0x000000016f378f30   pc: 0x00000001ee49a1dc cpsr: 0x40000000
   esr: 0x56000080  Address size fault


Binary Images:
        0x100a84000 -         0x100bc7fff Runner arm64  <54661672f2c5327790f053fac3b84474> /var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Runner
        0x100ca4000 -         0x100cb3fff FBLPromises arm64  <e18119c35f513cd7959d76a7084748fb> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FBLPromises.framework/FBLPromises
        0x100cd0000 -         0x100cd7fff FirebaseAppCheckInterop arm64  <3af839ea28da3e1491daad9083c6fa2a> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseAppCheckInterop.framework/FirebaseAppCheckInterop
        0x100ce8000 -         0x100ceffff FirebaseAuthInterop arm64  <112b87b75ffc3615bd6b042a4d24bafb> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseAuthInterop.framework/FirebaseAuthInterop
        0x100d00000 -         0x100d13fff FirebaseCore arm64  <76cdba1c722633c5b3133ba2032c4c12> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore
        0x100d34000 -         0x100d3bfff FirebaseCoreExtension arm64  <95cdd56268c43ef7b3db613dc9890203> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension
        0x100d48000 -         0x100d63fff FirebaseCoreInternal arm64  <3d964005f8c73203b937bb4883f5a76e> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal
        0x100da4000 -         0x100e2bfff FirebaseDatabase arm64  <cc23eb3e8a8539f39038072d15628f28> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseDatabase.framework/FirebaseDatabase
        0x100f08000 -         0x100f0ffff RecaptchaInterop arm64  <f48966b91d8f3f3ebfac25c27b787e30> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/RecaptchaInterop.framework/RecaptchaInterop
        0x100f1c000 -         0x100f23fff nanopb arm64  <e30f36190887380ab2a8170ac9acea0a> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/nanopb.framework/nanopb
        0x101070000 -         0x10116ffff FirebaseAuth arm64  <82a96d96b43c38a59a0794fabecd6738> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseAuth.framework/FirebaseAuth
        0x101304000 -         0x10131ffff FirebaseFirestore arm64  <f1042786cb963cb8ad82fc836a60984b> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseFirestore.framework/FirebaseFirestore
        0x101368000 -         0x101573fff FirebaseFirestoreInternal arm64  <418abd887f3a3f4494c3046b561dda78> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal
        0x101844000 -         0x10185bfff FirebaseInstallations arm64  <884b1f1480dd36c78847fdebf2eee75a> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations
        0x101888000 -         0x1018b7fff FirebaseSharedSwift arm64  <9b46b9bd9e253eedafa40fab2675399f> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/FirebaseSharedSwift.framework/FirebaseSharedSwift
        0x101910000 -         0x101937fff GTMSessionFetcher arm64  <dd6aeaf6fcf8384a9c8d338beb771eb6> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher
        0x101974000 -         0x101993fff GoogleUtilities arm64  <d6f1ea3d27b33f2dae7b607b596896c1> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities
        0x1019bc000 -         0x101a3ffff absl arm64  <dd9cbb9a454933a8a39204c00aab2adc> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/absl.framework/absl
        0x101b0c000 -         0x101ffffff grpc arm64  <d25f23033e043d1aa470b19dd8a95129> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/grpc.framework/grpc
        0x1027e4000 -         0x102827fff grpcpp arm64  <96648e85f4a83a0baab4e009de5b7769> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/grpcpp.framework/grpcpp
        0x1028c8000 -         0x1028f7fff leveldb arm64  <8ccc693d66bc3259a35834a8646f10de> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/leveldb.framework/leveldb
        0x10293c000 -         0x102a47fff openssl_grpc arm64  <a6a3830dac7a313498e4cca5019442c7> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/openssl_grpc.framework/openssl_grpc
        0x102b5c000 -         0x102b6ffff shared_preferences_foundation arm64  <0adc19c0e890344d975247caf680fe17> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation
        0x102b90000 -         0x103323fff Flutter arm64  <4c4c441e55553144a17c628d000b1236> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/Flutter.framework/Flutter
        0x103d2c000 -         0x103d37fff libobjc-trampolines.dylib arm64e  <9136d8ba22ff3f129caddfc4c6dc51de> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x107db0000 -         0x1082effff App arm64  <e038bc6c419e36a39f5464f9ce9f6134> /private/var/containers/Bundle/Application/43029083-7500-4030-9D23-8ADEEB07D311/Runner.app/Frameworks/App.framework/App
        0x19a7f4000 -         0x19a845bb3 libobjc.A.dylib arm64e  <ed7c5fc7ddc734249c44db56f51b8be2> /usr/lib/libobjc.A.dylib
        0x19beeb000 -         0x19cb5eddf Foundation arm64e  <34de055d8683380a9198c3347211d13d> /System/Library/Frameworks/Foundation.framework/Foundation
        0x19cdd1000 -         0x19d02b61f Metal arm64e  <22d2b05e8a76393e937aeb7651cc4c13> /System/Library/Frameworks/Metal.framework/Metal
        0x19d271000 -         0x19d7edfff CoreFoundation arm64e  <7821f73c378b3a10be90ef526b7dba93> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x19fb60000 -         0x1a1aa1b5f UIKitCore arm64e  <96636f64106f30c8a78082dcebb0f443> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1a51fb000 -         0x1a5240b1f libdispatch.dylib arm64e  <395da84f715d334e8d41a16cd93fc83c> /usr/lib/system/libdispatch.dylib
        0x1a5241000 -         0x1a52c08ef libsystem_c.dylib arm64e  <93f93d7c245f3395822dec61ffae79cf> /usr/lib/system/libsystem_c.dylib
        0x1c4119000 -         0x1c41b3857 dyld arm64e  <86d5253d4fd136f3b4ab25982c90cbf4> /usr/lib/dyld
        0x1ea460000 -         0x1ea468c7f GraphicsServices arm64e  <5ba62c226d3731999dfd0e0f7abebfa9> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1ee48f000 -         0x1ee4c8ebf libsystem_kernel.dylib arm64e  <9e195be11733345ea9bf50d0d7059647> /usr/lib/system/libsystem_kernel.dylib
        0x2278b3000 -         0x2278d0fff libc++abi.dylib arm64e  <a360ea66d985389394b96bba7bd8a6df> /usr/lib/libc++abi.dylib
        0x227998000 -         0x2279a43f3 libsystem_pthread.dylib arm64e  <b37430d8e3af33e481e1faed9ee26e8a> /usr/lib/system/libsystem_pthread.dylib

EOF
