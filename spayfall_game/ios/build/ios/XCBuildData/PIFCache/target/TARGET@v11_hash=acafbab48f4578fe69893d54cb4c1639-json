{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984252b07977683819eeac6259a081da0a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eb15fed1cec0b5c86def66bba421e46f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd1fe0296292fcea36d7abf16f3197e1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bdf3db63aec00bce737c72b32d26e04b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd1fe0296292fcea36d7abf16f3197e1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e4c46e88f150a2643c0f434f93a0107", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6e47ed6781ea17eddfa80045bbba67a", "guid": "bfdfe7dc352907fc980b868725387e9832293b6e568aa95f95cbfcefa167f0d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b4c99fb1eb0d8d11128584bda6c83e7", "guid": "bfdfe7dc352907fc980b868725387e98e09eee39352302952e136c66dec32abe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e906fdaef242dffac5fbe9597a8d7b71", "guid": "bfdfe7dc352907fc980b868725387e983c1fd82e2ff1ee570231b13c4519cf26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3f4c9025a3ad28ebf15822a96f54296", "guid": "bfdfe7dc352907fc980b868725387e98b6c53bdde2ca48ed8401753015b96e93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb4170e3bbd803f36b35d78a2c319387", "guid": "bfdfe7dc352907fc980b868725387e986a6c3507ae4c2f5c7eacf788db73fa13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b95f6c6830225f4a75b65693d3c6b4", "guid": "bfdfe7dc352907fc980b868725387e98b8865fa019c59019a32d7ab2537ad37f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f369f9f41e5cd1e48b36d183efc1683b", "guid": "bfdfe7dc352907fc980b868725387e98a194f6f3349bcfc7c5727ea5f1a68680", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98242e3d2b8492014a14e35c20de0d79bc", "guid": "bfdfe7dc352907fc980b868725387e9872b331da731e455bcc105cf5ddaa73d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98746d71c9a50298e3a853327b24453c42", "guid": "bfdfe7dc352907fc980b868725387e980cd2118cffa093ab397882e20ad6ca51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dcdb795a173a1e3cb523c599831af9d", "guid": "bfdfe7dc352907fc980b868725387e98924e1d44e0f320e95a7066b70fa711d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d9b5228d5fb0496f70cee6281dd0007", "guid": "bfdfe7dc352907fc980b868725387e989eeac06be62ca25135c8ad48de9028ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98072a230c7f7f23191dea35e7e11106ce", "guid": "bfdfe7dc352907fc980b868725387e9883477b83493e9f872b4edc568a99c08f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c04650dbf89b49efee35afc4591b550", "guid": "bfdfe7dc352907fc980b868725387e9867d2a2c4c677d47d9ad87e9ca9529960", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829a2eda34f44923062b9fe7810365339", "guid": "bfdfe7dc352907fc980b868725387e986f96e3f5dc00999d6006fe411f0fdf82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982877f4a3fbc6797ea572ae9c2a1e0d20", "guid": "bfdfe7dc352907fc980b868725387e982dc5fe8a65794a2a509cc96a31c4e5c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98268019e5a6b5fbf74879e6565915ed86", "guid": "bfdfe7dc352907fc980b868725387e98faa4bbe095c9c75c229b1082538b4961", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8d99cdf5f3b654f2a429efd34138904", "guid": "bfdfe7dc352907fc980b868725387e983ad192f0971bc737af57cc286ba0a0f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceab1d842bcb6260ccb58fdf531d8c00", "guid": "bfdfe7dc352907fc980b868725387e986c6b9a8114f1c9056f1bc4168def0161", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff48ba04fe7650f52f3a54e1650c2610", "guid": "bfdfe7dc352907fc980b868725387e984e06f497511be1d92d741705f25b9ecd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8d923db7dc8eb573a9bec22a1f531ab", "guid": "bfdfe7dc352907fc980b868725387e980fed45a01a082e3062e2f9600d1d8e29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689d948782325950a85bfcbb0deadaf4", "guid": "bfdfe7dc352907fc980b868725387e98c837496d7d39fcb9bd19332547cd9bd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb9448f0ffd49e4fa03790e124fa9940", "guid": "bfdfe7dc352907fc980b868725387e987cb5ec5fce7056c442afb5571832e5d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c7d0a3f2c54ab93be367e40af11b69", "guid": "bfdfe7dc352907fc980b868725387e985a427fe8eedbc460b9247195c736dbc6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e3d57fe20a2a0a1a1a92899bbd4dac7", "guid": "bfdfe7dc352907fc980b868725387e98d7993fe2ff76641a57a8a4e3ef01cf35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a60d8e57dd96957e537a2f73c71a305", "guid": "bfdfe7dc352907fc980b868725387e989b1bd6776fd6e73b9dcfff121616fff8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e188e2f7723c7ad5c9ab022529777c1e", "guid": "bfdfe7dc352907fc980b868725387e98fadfac0d5686b01e6d1bf590629d9689", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801943efd5725bdb399e8e51b5b9bbdd2", "guid": "bfdfe7dc352907fc980b868725387e98e937aa736b5ae55b53b651cf98e23b58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825bd616a57f7d0393ccac9b2e48c90ef", "guid": "bfdfe7dc352907fc980b868725387e98d358a09dcd4113277ed662f2bae1c2aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e277ad0811e7667b658a843dce8abb0b", "guid": "bfdfe7dc352907fc980b868725387e987734875e1043de216b10cf40a805c187", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824655c0a939c6a1e74f1dfe5b8fec3ac", "guid": "bfdfe7dc352907fc980b868725387e98abd08dfdbf3e6e94d7298c3fdb13ae48", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989f2d522253d8e7a5e764438b6911aa43", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c532b2c724639c89205d975abc343ddc", "guid": "bfdfe7dc352907fc980b868725387e987c274609e71f6b5e61744f1573272de0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98187f339187fda02372a12bf2042ef637", "guid": "bfdfe7dc352907fc980b868725387e9866c25c3645bbb15efdf11f7a273a8fd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9946878e6ca2249b8c7982a796a884b", "guid": "bfdfe7dc352907fc980b868725387e98918fa8e65a53109e12ec88d8062ed16b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989acec4ff89d469c456ac727d2c64d63f", "guid": "bfdfe7dc352907fc980b868725387e987edb7c35fb7e39119b706dacf2f2cef0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823fd73538f526f143f25b5becf045ffa", "guid": "bfdfe7dc352907fc980b868725387e9801b4f94fe10259a2249519df09e91002"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98839b7f4840ff19f592ae680b3dcb555e", "guid": "bfdfe7dc352907fc980b868725387e9877abc84757acb3e43a2c282cbcccdb04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838f39920795bb5a6b6b7d938c7f08449", "guid": "bfdfe7dc352907fc980b868725387e98224dc4325a64b5386dbee1b40d638769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98191ae01d5ada41f561eeee1aec700542", "guid": "bfdfe7dc352907fc980b868725387e987d66b1f00086bfef602338d5526f85f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f80a3d60225efdc7fcc39c5a66188017", "guid": "bfdfe7dc352907fc980b868725387e98f5a63de21a54f28aa0b94b4fb08a63a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988961d8266059c28c3579bc90d7925a1b", "guid": "bfdfe7dc352907fc980b868725387e989be07eba9f91d954d8a0afe9757bea4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495dbb10cf5e83a68fd9f33a8c4a1d40", "guid": "bfdfe7dc352907fc980b868725387e98f46e1c043bdbc012a9ba25554a5f41a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b94aa4489e8dbc073e789f5d33bf233", "guid": "bfdfe7dc352907fc980b868725387e980b07debca7058f7bf18c21389d3b5240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd8b1111fa5dd715e9f657c52546928", "guid": "bfdfe7dc352907fc980b868725387e986c6cebb6472916d2187a8cf1a5df1f86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1c2bdeb08de03e36cfd652631cb918", "guid": "bfdfe7dc352907fc980b868725387e98a415b74a6b542a6341ba80ff9af03ff8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb4dfcee2892d112976efbd4c332ceba", "guid": "bfdfe7dc352907fc980b868725387e98aba4c4be6b4c8c06dc86d25c4971f201"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e5b029dde127d5e7672e427b7687efa", "guid": "bfdfe7dc352907fc980b868725387e9886d92979cf8d891a5a1046f342435062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335c7f3d119f0b188dc1662670313dfa", "guid": "bfdfe7dc352907fc980b868725387e987acdb56b04c3514334855b988dbca91b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6362210590dc1c5cd1ebc1e1aa238ac", "guid": "bfdfe7dc352907fc980b868725387e98b4b26a8348991ccd6bee58cc97ddfc68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b665fba322cba211af8b4e1b2ae117", "guid": "bfdfe7dc352907fc980b868725387e98e423a653955d249df0dcdc7d1d34ed77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c83735abe36e2ee66919888bab8850f2", "guid": "bfdfe7dc352907fc980b868725387e98966e93fcbbff197004529ed8432514be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f78e65c6606fb22b9abaae3a12be553", "guid": "bfdfe7dc352907fc980b868725387e986a8a93174193b653db37f69c82ea3a23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8cdca59b8dc255915e39bd824fc4226", "guid": "bfdfe7dc352907fc980b868725387e98d5ff47ec1b7b11d3b0c02537889a36e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e871703eb0f898571d76c2fdbebeeac", "guid": "bfdfe7dc352907fc980b868725387e98204781af06c2919fc7641082af52cda0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ec530a8b3d8651330b4d3a9e98f031c", "guid": "bfdfe7dc352907fc980b868725387e9878113c7e90eb90ba70aa4725e994cc11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989de6a7108d1f01a6dcd51837ca9131db", "guid": "bfdfe7dc352907fc980b868725387e98e2ea2876b5358b6ad8191a763939235d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836c6e7324fade987daf5da889fdf631d", "guid": "bfdfe7dc352907fc980b868725387e9828e2deecf5df97a16f17bd40a0d3a36f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f50a100e7e007e6c0cbea334a01deb1", "guid": "bfdfe7dc352907fc980b868725387e98d6301fc8816f4693b96997566bedfb18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98604e38d42b5554ac12a9ac0e1ad92e1e", "guid": "bfdfe7dc352907fc980b868725387e98d671692b7417779daf62a1f315fdba5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e86a652b77aed3b4a3fbb8c7ac876c", "guid": "bfdfe7dc352907fc980b868725387e989a2373a60b5448cddefddba38a34c210"}], "guid": "bfdfe7dc352907fc980b868725387e9816d0865dad7f80d7b85f2846b757406f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e989c93675d1e7b9d31c2bda6d98c8ea5cc"}], "guid": "bfdfe7dc352907fc980b868725387e988213f1a84add6a222d7f5b741772bf11", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4574829fea4a2b943dbc3ed32814fae", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98f571738cd69796c2e106b9c2ac5ec8f7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}