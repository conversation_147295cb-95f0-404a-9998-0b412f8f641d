{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d21851fc1efc426d9d2966cabb4c371e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a48674ed5c7da0e7767bdd6f32490cd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2719be8bd430516c7e826ec099212fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d2e41a39db42f30cf1ccefdd6e62cd04", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2719be8bd430516c7e826ec099212fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dad83d5bfa329f21a998b00f0f2fa389", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d275e22343f5bffcdbdce4a03b83f166", "guid": "bfdfe7dc352907fc980b868725387e986d1efd5250bb90f3b441e3f804bee62a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f09701d46514d6a12182e86e67ea668", "guid": "bfdfe7dc352907fc980b868725387e987a11c94a50e92ba469fcd8b060e6e7f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0b413f34f5e1eeabf9f4309ba45b89f", "guid": "bfdfe7dc352907fc980b868725387e98c45545607e4deb73e0c778a148fdc09e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137daf56d157bfa3b58e3d90ed512417", "guid": "bfdfe7dc352907fc980b868725387e9862080f0455b87c83d9420ad4edd29fad", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987805454cb4933b4e9f20fee58b23fce3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985a5d8538d78b90ea9ee0d480360d1b28", "guid": "bfdfe7dc352907fc980b868725387e98151f575697f0604af1ac777b29b51e3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98157bb6e92cc99bcf12ecdde02f054c48", "guid": "bfdfe7dc352907fc980b868725387e985967c81577cd074edc68e8dc7da79b61"}], "guid": "bfdfe7dc352907fc980b868725387e9847915fec25b02330937960b98b9d9310", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e980237eb1b38b536ed326b4e4d8e4f5ae1"}], "guid": "bfdfe7dc352907fc980b868725387e98bbb2be568dccc6c9e0d52344366b9490", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984180b6bc1c5c7da971b96002ac44a105", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982cdb0c7c817307e018cfb4299b646a42", "name": "FirebaseAppCheckInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}