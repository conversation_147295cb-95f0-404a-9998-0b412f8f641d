{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a7295a9805301e8548013b22004fbf77", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98620335c8dd39b59aeecf380f26db837e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f1a7883ca252ae3a932b84ec26dd5df", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989fbf1c446487e498fd97ad1566b80435", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f1a7883ca252ae3a932b84ec26dd5df", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c8149023ebc397974ba5d57531f45e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ec5aab30f98d25ef2718cce1e8cd0f7d", "guid": "bfdfe7dc352907fc980b868725387e98fd8cece3c8f283e467550a661a3daa24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce611762286ed2ce5504ba67ddbe2784", "guid": "bfdfe7dc352907fc980b868725387e98f39ac0aaa30bca0942942bd909bb9bd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f39214802f496b9382f7cba744ecc075", "guid": "bfdfe7dc352907fc980b868725387e989c181329c2ae0c14fc7bc7d786d2d1fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c11214dba883abcad819911d812a6f0", "guid": "bfdfe7dc352907fc980b868725387e980d073a6e3e6b55d9df14fa5fd8925b94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870364324ab589aa2e9d3eea8c2f5b855", "guid": "bfdfe7dc352907fc980b868725387e98a90071b8338f30746450c625b9932b40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b03012ff2fb85720061daeecd7e784d6", "guid": "bfdfe7dc352907fc980b868725387e9826697e42a6733a768c57ea1a034bfb18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852599847e98c7581652137290c0eca63", "guid": "bfdfe7dc352907fc980b868725387e987e8a79c7946ea02102fb34fe4b2aa971", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7e2d026fc34e8e872a24be8fbaf2456", "guid": "bfdfe7dc352907fc980b868725387e981cefe16dfbac5e915bda09c9500e8024", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811b0649a98d3885509a591a2402b5ed7", "guid": "bfdfe7dc352907fc980b868725387e98f68a70c5721b043c86ebd558ac3a007b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c445298e645c4f07dd5f7190f3cdaaaf", "guid": "bfdfe7dc352907fc980b868725387e98ea02d16e483ce20ef28381bbc4f2a3a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98394bf686cd4b00bc624ce54a79622501", "guid": "bfdfe7dc352907fc980b868725387e989bcd321fad8140565a495a4f1a55359b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7157cb1eadb278c6f9c5797e1eae8e", "guid": "bfdfe7dc352907fc980b868725387e980a2ffa96d3db90c0232ef5e8adaf45c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98057a36e2d551b4c33a13823e45cc65eb", "guid": "bfdfe7dc352907fc980b868725387e984c7a2532704753be7bd290aa16dce3e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e95dec5789914c70d040fca5a7fdee9", "guid": "bfdfe7dc352907fc980b868725387e986cb695c32aee727e8ecbb9a7bcadd2c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98785970e5645c3fd4e91b5831dc3f7a7b", "guid": "bfdfe7dc352907fc980b868725387e98aa8d0576b3b7e7657b517e32da4d296f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f27ebbdfdaf999ab97b3d663da6727", "guid": "bfdfe7dc352907fc980b868725387e987202302bfabd57ea4300dcb4b7d534af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6fabe72b36aa2dab6261115e5fa2768", "guid": "bfdfe7dc352907fc980b868725387e982f86932677c58c12fce26e0fc7a018b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812c0710bf368baf66cc6473454263a2a", "guid": "bfdfe7dc352907fc980b868725387e981d951d025107e989660dfd7412b8d90e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886ace018775000b3d769a8dec83379cc", "guid": "bfdfe7dc352907fc980b868725387e987a8da25afc970f47db1cd7a512368494", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898ef9c9addf6ac5ed0f98b2f4b550e6b", "guid": "bfdfe7dc352907fc980b868725387e9831007edc74f92c542851329308c9652a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98007ac8cd7060b603659b010ba5aad6c9", "guid": "bfdfe7dc352907fc980b868725387e98f274c3d6b7f29387536ee1f757633baf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d7afb4541afe0696cd52fa02f64b78f", "guid": "bfdfe7dc352907fc980b868725387e98067190cc426ddb95ed3185afe5bcefd3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982edd85c49b16366a989a55623abc5d59", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981cd255be5b0ffe943fc9b6f31e870b66", "guid": "bfdfe7dc352907fc980b868725387e9876548467450218e26b0226890e99b880"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849e99da773a90dc61771a8bb169b7836", "guid": "bfdfe7dc352907fc980b868725387e987fd9eb082859b43ae846d601f50a2770"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989610fa439d772bb7ed61b7278d8dbc76", "guid": "bfdfe7dc352907fc980b868725387e98344e3ebaf17217eb3ebca40d087faae3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaaa06cfed490b9ecaca0e5e5485570b", "guid": "bfdfe7dc352907fc980b868725387e98192d0866646a2037fa6e0d19c6dec1dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98720a25a29e86ce04fcc696e764498737", "guid": "bfdfe7dc352907fc980b868725387e987717849fa1c36f2b7020e57819d543ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abb16b922002cf6234534af289f76a80", "guid": "bfdfe7dc352907fc980b868725387e987e13384812baecb359bd1d40d6c70d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa8aff8f5d83bf2ada6733df759f37f9", "guid": "bfdfe7dc352907fc980b868725387e989d5722567d87d6e85b1621343fcb48b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987da084128b0c6116d645a3d3f5191d9f", "guid": "bfdfe7dc352907fc980b868725387e98baca99ab0a487d35ab23e99863d17c52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2f9f128e32d460a171e23f06305edd", "guid": "bfdfe7dc352907fc980b868725387e985ab843f105030d2ac5720152cd507bab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de24e49acb548a6644ffc7a198916fa", "guid": "bfdfe7dc352907fc980b868725387e9843095c02b3f97dac57af683b749c0630"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980909cae949752b665bb15152578498ab", "guid": "bfdfe7dc352907fc980b868725387e981d4e4bfca652c99488f6490159d20754"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b33de65d1142c736b31a6a84a31321", "guid": "bfdfe7dc352907fc980b868725387e98a21a390493f592b8ded9552569300e2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575e9d2dc56be9867e424ba49fd40ff6", "guid": "bfdfe7dc352907fc980b868725387e98a80fd5423ae4273422e271dffe612f26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887310a013be9b602d69672b3ff52f6d9", "guid": "bfdfe7dc352907fc980b868725387e989816a2ebbfd5ed306767b5a91b43d862"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813cddace8bb14d91566636a3e13d9f4d", "guid": "bfdfe7dc352907fc980b868725387e98a5ae1022f449a4a317403f4d77577f29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4aaf9712f275f5e374656fc8d789259", "guid": "bfdfe7dc352907fc980b868725387e9853ffe3dc8d72ed1cd4b0e5175ca341a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9e787a25355ac0bfa8bb51d86dfd6b4", "guid": "bfdfe7dc352907fc980b868725387e9856a63c74600b13a16ed36ed1126509fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812ce0dfee697e6ec815787b3759d59d4", "guid": "bfdfe7dc352907fc980b868725387e98c4e29334840feb3d4d35aeadad47d026"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae7c193c5110c26c42d66d42c66954c", "guid": "bfdfe7dc352907fc980b868725387e98b126c6adda2a7388fe2fc496c6207f51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a06ffa22136aacc0f9c743dc2895afee", "guid": "bfdfe7dc352907fc980b868725387e986dcd8896bcdf3adbee8a43e888a72eac"}], "guid": "bfdfe7dc352907fc980b868725387e9807aba37f1ccfa6cb4a65e3f41a063ec3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e98a2f97c2b9a85b64ea8c426004d63e56d"}], "guid": "bfdfe7dc352907fc980b868725387e98f9d0707c1c75b17af4ba34767a0eef43", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a172101cf51085891c1b40f28adb6137", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98fed27929ed02b380ccfda8af7808f61b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}