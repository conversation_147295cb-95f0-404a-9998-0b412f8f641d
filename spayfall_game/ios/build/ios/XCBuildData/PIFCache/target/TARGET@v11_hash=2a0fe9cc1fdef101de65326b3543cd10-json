{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ecdaf4e004aadeda6b9e50cc5f1e5bbb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.32.2/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.32.2/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cc7d428297825de71e0390b186d781c8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.32.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.32.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cc7d428297825de71e0390b186d781c8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.32.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.32.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b76ff587847746237f4eff6360504e7d", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98144fa72f47d90c33b374d1ea9c3a49ac", "guid": "bfdfe7dc352907fc980b868725387e98c2b31e7005a791ee940ce439ee70c12e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce6fe3c93de6e5e0de19e292518513d6", "guid": "bfdfe7dc352907fc980b868725387e98155fb02b6dcffe78684316cd50bd3cd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867db32a045d582ea55c116102e9fdbee", "guid": "bfdfe7dc352907fc980b868725387e984af64cb9667382548022c5704f79228f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aff27839b496fc4d8e0986c297390d1a", "guid": "bfdfe7dc352907fc980b868725387e98135e190762e60cde5b181b4a937a5f49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7e279a133ca16b4695ba0d1792a57e6", "guid": "bfdfe7dc352907fc980b868725387e984a64b99ec95353530573a9e060676741", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822f5d8e034a97d97d25bb41cfdc9b982", "guid": "bfdfe7dc352907fc980b868725387e9832c37732f5c0fa1c7debbffb81682ead", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98284c9db3b66a4f5a72d12050d3007026", "guid": "bfdfe7dc352907fc980b868725387e9876b296018fc7d007c2e10d33428a9734", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835ddd380d5b8a07a9257eb2ed54ce04a", "guid": "bfdfe7dc352907fc980b868725387e98f7d1f826869731d01189c29e48fa1d5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd238b6a936ac527fed5037cf6ac429", "guid": "bfdfe7dc352907fc980b868725387e988eebb5e373ad55f3ed0983ff554e8539", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3807f37e348d57ce9f6767dc63c0d7c", "guid": "bfdfe7dc352907fc980b868725387e981aef5eaf361eb4cdeb7cb666b27d530c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818eabb799680336210c1cc9a74788fa", "guid": "bfdfe7dc352907fc980b868725387e98470c99b933039d297db3ea9232bd7d24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7aaeb8cb9ff825c143aebd457cd9848", "guid": "bfdfe7dc352907fc980b868725387e984dd915053e75f470ccc352b72eb3bb54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d8b791310b25ca61642c1ec3c77cc1", "guid": "bfdfe7dc352907fc980b868725387e985759f567c8f237e696f439ae873744e2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd5c9b4a17a8e31590a3a5d9d39bd938", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eadf45394b772e6720c5b750beab8c0b", "guid": "bfdfe7dc352907fc980b868725387e980ea080d90a0d0c8cd8b8b975957493ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a65bfa25f386fb8285f0ca221e4e2728", "guid": "bfdfe7dc352907fc980b868725387e9806e317866e8e421d5beaee24ca03372d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885c62c05c60357c82042e79d3f4785c1", "guid": "bfdfe7dc352907fc980b868725387e980bf3ad552e25856d4d24dd168b76f98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad4f2b589a673e982108fc1b0cc27bcb", "guid": "bfdfe7dc352907fc980b868725387e986ac1dc093b57f6ebdf24f16e195bb13c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985385fee68185b4d609baaa3d0991d843", "guid": "bfdfe7dc352907fc980b868725387e982d61cdf3c9aad848a4a146127408c724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8381bf8e39aac84685f6e52e349abd5", "guid": "bfdfe7dc352907fc980b868725387e98fb9d86bd032a961dc29580eb63f8065c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c35f73de1d69ecf9cf1e29ccfe7a92e", "guid": "bfdfe7dc352907fc980b868725387e9866ac2ab57c59067a2ec5b654002c2367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b5442efe6510f3503ca957782a56d1", "guid": "bfdfe7dc352907fc980b868725387e98c518ccd92119ad2b028cd9b2923f5e3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982422b954f608f5114e84f2443810c5a4", "guid": "bfdfe7dc352907fc980b868725387e98b1e2e5d0e206fb768709c37ab6336407"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae0bf6942e1f478773972aa973b81e24", "guid": "bfdfe7dc352907fc980b868725387e98c0c1380224d69f0880b27744e2305850"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888e706b346e7f8d6608870658d1caffb", "guid": "bfdfe7dc352907fc980b868725387e9816b0cbbbf04f6676598b49ac2ba09bb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfd294cce05f22e4ac11027e1110ca98", "guid": "bfdfe7dc352907fc980b868725387e98656cb74788b18c69cfcbe572419399f8"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}