{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fc134bfff303d5b03eaec08221f0dd6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cb94e8e00b001156a5c289a6c26371bc", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b5b61a200e86ad82f54b77b4fe2a386f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a6b08cbfd4ee1e8326e9ccab9a4545b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b5b61a200e86ad82f54b77b4fe2a386f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985cc6e74b0a7a7e56e184363f827a6490", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c55d611bf6b0d8eb558d36f0373c3c4b", "guid": "bfdfe7dc352907fc980b868725387e983735c2824610ec0120a741d582fb2a42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0553e940034c5b7ecc449a895b8e983", "guid": "bfdfe7dc352907fc980b868725387e9803d4678d94274c065d2e90aca40caf98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b6cefa7f74500254fdcd602fe32fd0f", "guid": "bfdfe7dc352907fc980b868725387e986a46caa08b1d300336a06b5f48b17fb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850af071db611e8037d3cd3c1285a6cb2", "guid": "bfdfe7dc352907fc980b868725387e98ea254ef066918c7fe9222bc705f32e86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826861b5b4ca35b134ed73ab4ac14df50", "guid": "bfdfe7dc352907fc980b868725387e9826828ef936531623e6a8cb88eca0dcca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d9c98ca4d4f6e1a8f5cf30b7a954645", "guid": "bfdfe7dc352907fc980b868725387e988d31ec3740702e19f6885b3219f795fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98978db927bf34d84dc7c5f1a9ddec503f", "guid": "bfdfe7dc352907fc980b868725387e98fd55e7954b1f4473adb7e550bdc024eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e11e0c4e5e2158a94ba60eb653b356d7", "guid": "bfdfe7dc352907fc980b868725387e98dac298f33a9530cef3d979ded6f00dbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98511c9548081fcbd92fe592c112b3710a", "guid": "bfdfe7dc352907fc980b868725387e98262bf9154f38a7ce89ac28b3e3a53b0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d15d45123a828c75a27d94e8fb8a58df", "guid": "bfdfe7dc352907fc980b868725387e98a9bb5a0b109e16e8454753ef8595caec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823047dcc21a0f0aeba195944b22a1abf", "guid": "bfdfe7dc352907fc980b868725387e986dadf31591fdda2d9b3f5a96ead0cc45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b332e94de03887ca2b709b6091282652", "guid": "bfdfe7dc352907fc980b868725387e988be48df855153878653be35d87c04c22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814238152b3f13bc93a833daac10e8f31", "guid": "bfdfe7dc352907fc980b868725387e985df90e16750ffb1a0d3aa8f567021e46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852e1c221e0c073e96716c7a7b83d8df1", "guid": "bfdfe7dc352907fc980b868725387e9844179718e2637ab6d53d6a50393d18a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876a299ed8554d7e062dd8afe55b0097c", "guid": "bfdfe7dc352907fc980b868725387e98920615767c6f8e78984eec254f6df616"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c550c1d0c387b136c79c478a6d181c5", "guid": "bfdfe7dc352907fc980b868725387e9899fa6897d03084d4456638eac10d9da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a7db4eb16c31694e60ec4b68f86c65", "guid": "bfdfe7dc352907fc980b868725387e98732d3d3cc42afff53369e514d298de20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0baeb47fbea60521509a0489bc247fc", "guid": "bfdfe7dc352907fc980b868725387e98cc35890d362048325aeb3fbe42b192a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980390c3d9903dc048ba840f835045a91c", "guid": "bfdfe7dc352907fc980b868725387e98e122c37dccb50a269b72df1a2d76e4f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf1ce1cf56572b3ebbf82b49f6c4c8a7", "guid": "bfdfe7dc352907fc980b868725387e98b6b7ac3d6cfc0feea517f4323a555ed1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c262daef2ab623384f975d112e80e75a", "guid": "bfdfe7dc352907fc980b868725387e98ed606aaf2abe8aa5b49b85dee0aa951a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfb73a9f8010853547a1b3a6ddb92daf", "guid": "bfdfe7dc352907fc980b868725387e988ab114c2823cef8a25306f4933505ba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7383ce79937ec6b43c7f55fe2934878", "guid": "bfdfe7dc352907fc980b868725387e983cc19c20ea0efd9b6f77521a657366cc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986ae69f13eaf7b69f62f1fd04dc2dd316", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a70bb17022d68d7ba40fc5402d3421f3", "guid": "bfdfe7dc352907fc980b868725387e98177d2d82e084c4c5e7e2f031996339e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ac2dc9c603746dd9544da32149850a", "guid": "bfdfe7dc352907fc980b868725387e988034f63539a56a7c10f5b9aa68c6ed98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197eb41acacbb4ce564ece6e95c076e6", "guid": "bfdfe7dc352907fc980b868725387e987b93a1b0979f51790d77b869f27b6edd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5d799a696eea8d02672eb3240218432", "guid": "bfdfe7dc352907fc980b868725387e988aa1f673f5b25da18eaac6963f6a0d73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f897bebe42910f0267cae5bb3e0a4a45", "guid": "bfdfe7dc352907fc980b868725387e98e8631a89154479996d2c6a756dbb57c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868545f045f416d398c4b2f62100dc27d", "guid": "bfdfe7dc352907fc980b868725387e9866f14e2415baa941400313fa2fbf9073"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d02a3fdbe90799e8395606a8684e39e6", "guid": "bfdfe7dc352907fc980b868725387e98db29324b0c0666708a1c5c9fe069ded1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fedf82aa28d3fa60b1ec1ee38499bf4", "guid": "bfdfe7dc352907fc980b868725387e98a11f7e9178149e67e81f5812b53593e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8772363dbbd0fb15f622575ee7391a", "guid": "bfdfe7dc352907fc980b868725387e986561c4042b802ed0fa14398781c08517"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98474bd4a377c1f978f72e5ad29f033e04", "guid": "bfdfe7dc352907fc980b868725387e984cfef2dc09f5d00a5285571b5b384107"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63acd21ea6b87bcfa4ca3ad11849d92", "guid": "bfdfe7dc352907fc980b868725387e9864d939acc72336c4b8b2acbfcba971b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986109c905c3bb03204bbfebdd71abb90e", "guid": "bfdfe7dc352907fc980b868725387e98bc1da563dff627094dfbc84585a57383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c5b37648a8167aa01d6d7d77ef88c76", "guid": "bfdfe7dc352907fc980b868725387e9859fd89630c17875f7d6766e9979d28ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc1ba7ec33601018c69949bab5d3412", "guid": "bfdfe7dc352907fc980b868725387e98222b3243f2780e2361ded158e3563b20"}], "guid": "bfdfe7dc352907fc980b868725387e984fff01babfb9ca26b654a81930911932", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e9832679b6f509ba0a992b5e280d24585da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fa1bd45a35be6ec7a7d9602c5588655", "guid": "bfdfe7dc352907fc980b868725387e98214125706b94f83ad8ff63259f5b0f5b"}], "guid": "bfdfe7dc352907fc980b868725387e9854648250ec43c1fcef0019965d801a5a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9822e1aaa626152f958f71328a74423bcf", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98382c0f698d1411e8e94d61967593c5de", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}