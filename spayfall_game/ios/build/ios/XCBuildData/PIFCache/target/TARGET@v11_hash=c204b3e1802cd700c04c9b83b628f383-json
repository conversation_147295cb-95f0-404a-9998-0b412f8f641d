{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980b7ecffe3541519d140d62139e6bea68", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98013f6581082621cbd59d16fffe072325", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983dd6ddd9c21db57985e436a95c729a54", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984a67393cdb045806f470641f7e2cb033", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983dd6ddd9c21db57985e436a95c729a54", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98664a8614a985c0350b048c9b03306cb6", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d0a34dcf4d8e534bb1a2aaaf66961d4a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987e1c2ce7df453a91f2c48d4bd818e28a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f73eb0fb0d6d9fd4a5766167d42a9897", "guid": "bfdfe7dc352907fc980b868725387e983b7b9039f68fd28e9bc2f2742a240fe2"}], "guid": "bfdfe7dc352907fc980b868725387e9827b991fce22827f2cf1ad951c1fe282d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984fe1f454389a944b317683cfdba2e41e", "name": "leveldb-library-leveldb_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980fe361aa6bc047147e11f30537be10ee", "name": "leveldb_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}