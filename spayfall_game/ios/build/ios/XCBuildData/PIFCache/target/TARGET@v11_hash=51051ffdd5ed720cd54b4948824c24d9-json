{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d089c0f9eb8ef56c5f48225674d0e24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dd6700e48b62e2ed33da4795cc773f9d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989419060ab779752f3c068faaea8f690c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9851edf42ad03fd1f78a9d72897255048f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989419060ab779752f3c068faaea8f690c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f359c8d3e356e2aa0ad6fdaddab781bb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b2b05a504c5e4ccd20ac0f5f381640ce", "guid": "bfdfe7dc352907fc980b868725387e9837624fbfaceabfff46b4dbab9f2dd515", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9855d7f973cdfeae21bb2072dcf79788da", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98053c083a7ee0db2d770b42ad418da6b0", "guid": "bfdfe7dc352907fc980b868725387e984e8ba65c76a9bb9c84394cbe184de50e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be275a592f33488b2724fded7d791ac5", "guid": "bfdfe7dc352907fc980b868725387e989bae6f65fbb41c0faa05b94f23e8371e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0501a947e1dfa6ad471f7cad3f4cef0", "guid": "bfdfe7dc352907fc980b868725387e98eede0c411f2ccc7f7d931bd1628f17bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d478d06d04b083bfbc7f23bd25889d2f", "guid": "bfdfe7dc352907fc980b868725387e9862fc9160d0e81edfa19fa1152c0ac0ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73eb6d17a20da77bb3d7325350e774d", "guid": "bfdfe7dc352907fc980b868725387e9873c2d2e378e0cf8567c5b11d2e4f5168"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98644a5f808f9fcf14adec3a57cb487eba", "guid": "bfdfe7dc352907fc980b868725387e98602f8b59739e0aea4352e59d467ed031"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98287d72948415aa4ad8e9bd2be45ea79b", "guid": "bfdfe7dc352907fc980b868725387e989515a9002fa8651127acb2897518a2df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983560d32522c8024aee5fc0fe4d4d02ff", "guid": "bfdfe7dc352907fc980b868725387e9804eeb239eb58d7017fbf144e51bc6eff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883810f9071416daa86c4f20f870daa0c", "guid": "bfdfe7dc352907fc980b868725387e98ebf22888448638920a95053f6943a73d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d06ebf53ddb6e50b7f246dd0a8f443", "guid": "bfdfe7dc352907fc980b868725387e98c558aa1afba50b9196fbad0bfdd7b796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825135c817d2864a71dc0f371c8a73d17", "guid": "bfdfe7dc352907fc980b868725387e98e9f50fd4a604b3e02871b9e23521f726"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad3660982ec2c5a9eefc3cabb058c5e2", "guid": "bfdfe7dc352907fc980b868725387e98a18630d5accedf3e96d0712007ab0d47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e648fb2761826e9db3be30135ff1294", "guid": "bfdfe7dc352907fc980b868725387e98d81bed49367c3386d1d7a13377dfcdae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70b74c53040a98cb4ed4b7533aca94d", "guid": "bfdfe7dc352907fc980b868725387e98467841860add628210ce83dd17e5a92a"}], "guid": "bfdfe7dc352907fc980b868725387e987c7de6e38991d01510970fc402648d25", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e98de40688eea98e4ec87c55e9453a564ef"}], "guid": "bfdfe7dc352907fc980b868725387e98465501782e0e29ac11141b0f5178e62e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98df28e544b6e9e633d85973e099c80b1c", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e989432f0b6a7ce84f5f14dc64154969a35", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}