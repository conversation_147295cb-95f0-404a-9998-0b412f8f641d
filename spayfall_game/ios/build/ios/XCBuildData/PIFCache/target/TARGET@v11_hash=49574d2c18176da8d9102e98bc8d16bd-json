{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cff792163a03dfd47a8209cb9aac873f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987bd610f027d07d1558f268c81877642a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8ff342302edbab8d830442c33fe7bcd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98410506b2af8190b6efa6ebf00fc762b3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8ff342302edbab8d830442c33fe7bcd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987e7e6a69c9acb39fd41f8c099b927044", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bd044cd40e77eebe2ee9561e1c1bcb78", "guid": "bfdfe7dc352907fc980b868725387e983b047371403095bb105ce69f269a3757"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809a96d10aaa4ae0c7866bb8e35048819", "guid": "bfdfe7dc352907fc980b868725387e985c8bbc02ee67840e7a23b3c64ba94eab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b37670458d79b33650e30ebc5d27b78e", "guid": "bfdfe7dc352907fc980b868725387e98e708dff9d2d0a3bb77c1c530997cb03b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b50abdd6f35960ef89f503e1935e9151", "guid": "bfdfe7dc352907fc980b868725387e9815e5eb37e56715dd5b3865106e721830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd54dad01db20e9966b5b9da30e42cae", "guid": "bfdfe7dc352907fc980b868725387e98b0fa8f3dfbd0fb44226fbeb2d760b5bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98763d70d1b0badebe868a9dd08f65f33a", "guid": "bfdfe7dc352907fc980b868725387e981dfda63bb87a4daa71e99a06d69a39b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814da2acfb48f82bcbf080bf961acab60", "guid": "bfdfe7dc352907fc980b868725387e98d5a25c90ff0ec1ed31328dd5f7ea2731", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894fc4fba9aaa143dbed83cee8c99065c", "guid": "bfdfe7dc352907fc980b868725387e98ccb924702948ce164a4a580ca3aaf53e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc8c6607251cabbceeeac85763eacd03", "guid": "bfdfe7dc352907fc980b868725387e988bab761f00e70e92299b94a50cf5732d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834d1e564b3a2f4782cc811d74d387681", "guid": "bfdfe7dc352907fc980b868725387e98e10389564c59601e19944142c05877a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70b777a8a752b0c38af44b43e2a9981", "guid": "bfdfe7dc352907fc980b868725387e980b3cb6ba2fe0fc85aa434ca5576d27b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cd9475352abc298cc4e8d933dff5f12", "guid": "bfdfe7dc352907fc980b868725387e98a091b363bf927fc0c0d0e5b87d626abd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988706093d532dfff591d9b464a15568b6", "guid": "bfdfe7dc352907fc980b868725387e9885592441dc7d99c2fdeef8796627039f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98866b4111776e0abba878aed22442e8f6", "guid": "bfdfe7dc352907fc980b868725387e98f74dc4ec92ac44478b73324941c93dd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6976fc2d71c92e1fcc1a3ba509ae47b", "guid": "bfdfe7dc352907fc980b868725387e982d56609704692058d8a6480bc54d51f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b8eff5a6804411e6bc88b0b69f3a620", "guid": "bfdfe7dc352907fc980b868725387e98547d627b056a66184d61a5bba31b38ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984686ca59cb3e7d5671e6e51296dbf27e", "guid": "bfdfe7dc352907fc980b868725387e98dc439a176d37068e3681895d65feaf42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884f93debc8bdb9a026fbe9ec8643b224", "guid": "bfdfe7dc352907fc980b868725387e98b04f71b21b6332d89a0ce6eb9b650e56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98328a2ee34b937d4e32a2d0507442379a", "guid": "bfdfe7dc352907fc980b868725387e98dad0f4c9f0d75074b68b0d72464322c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881045ca3934dd1d0efc8813988d06de0", "guid": "bfdfe7dc352907fc980b868725387e9869744cab48321a28f7634bfc9089119e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d838aed841f24feff2125dc97d787d17", "guid": "bfdfe7dc352907fc980b868725387e986b306b316213ae69f27323209bac206e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818c85ccadbe14b715a490ffb17f18817", "guid": "bfdfe7dc352907fc980b868725387e9832c4ff66124c1b06675892f1a1476bcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c22916f61ad4bf48af51ee50102ef0f", "guid": "bfdfe7dc352907fc980b868725387e980f8ae91a0eef4ea49f82bbe941a069ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871d3ec4737572f5cda08559ecb7736a2", "guid": "bfdfe7dc352907fc980b868725387e98b00ba80c5be09ee0a8126e9314624220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e9523965cc0bc889bab44857a4a81e5", "guid": "bfdfe7dc352907fc980b868725387e982e14fb3d9fb56a145e5b546573a4283d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a83ad8ff8bbd4da94399102c35e1ae1a", "guid": "bfdfe7dc352907fc980b868725387e9843420b996056594d0a6546e4430ffa3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc7db68e9bdc4270112593e364dcad1c", "guid": "bfdfe7dc352907fc980b868725387e987e0034b80eda4448e432be68132a3a46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5bef34a3cb6f1c8940a1dace29d9ded", "guid": "bfdfe7dc352907fc980b868725387e98cb4ce9442b7a27550b4b5cfe2d1bf2c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d077d4b2a5aef6998ac0cdfb48fa841f", "guid": "bfdfe7dc352907fc980b868725387e98f0af9222ca989d828d460fe57fbb1eb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ada7cf897cac5ea1bef30a98a4c0d31b", "guid": "bfdfe7dc352907fc980b868725387e9830a3b109648f65dd6be719c7fe6931ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a83123244ec07a0e3ba4bb5cf48dbda0", "guid": "bfdfe7dc352907fc980b868725387e98870e47e584762f1e8406dd26e52ca880"}], "guid": "bfdfe7dc352907fc980b868725387e98c9939e09ec0071a18b9f0e64bae9f9b2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987208e9895d42c88728982a26542e8832", "guid": "bfdfe7dc352907fc980b868725387e981d4995260bfcba206ddf1defe78c1e26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b10862b282f234678810286f0fcd44", "guid": "bfdfe7dc352907fc980b868725387e98cfa1d4f794311215aec9a2e913624c08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176f950c6e66daef5619e2454f417681", "guid": "bfdfe7dc352907fc980b868725387e98d7cd62d33acf8ccab7b7ed4a53d0222a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989739941570f75fc59e5bed3a71080bb8", "guid": "bfdfe7dc352907fc980b868725387e989972dc13b4b94153aa470133a5efb993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a0aed08e0b13cc3b3880ceb4e9a13e4", "guid": "bfdfe7dc352907fc980b868725387e9836faeeeaaeee06f847320da79b73935d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980992307019d3cc0e072f24628eb70b11", "guid": "bfdfe7dc352907fc980b868725387e98f51ee1909d85641bd106ab853ee9ea3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98459cf26788a422cc28ece427fc640485", "guid": "bfdfe7dc352907fc980b868725387e98f4063ccfdc2a6de8b980bd649ab99b29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a18fd9cc4ab2a3e2548dc55826f192ce", "guid": "bfdfe7dc352907fc980b868725387e9882cccd8d0d12a25a806bfb3eaa179715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e687dc849fbcdc1d98f8d5454323aca", "guid": "bfdfe7dc352907fc980b868725387e983d9d74d0a09583a216caf15d8f8963c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5e0eef4aa0556ab8cad38a1fc27e26", "guid": "bfdfe7dc352907fc980b868725387e9846741869c4c0dade6491e36ee098bbaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae9dafa9415658011a75f02aaec7c830", "guid": "bfdfe7dc352907fc980b868725387e98c43b165516377064617e63b86af387fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818ad051eaaced9a158c6b5e69723f65b", "guid": "bfdfe7dc352907fc980b868725387e98017797ddda60ec88b2eb135db29d8c17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ff707c03da10fc998d7424d087d0e0", "guid": "bfdfe7dc352907fc980b868725387e98b3105c2e398ba2afaaebce0a5994f28b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92e80829cd2ae16fdc8ffddbcbb574d", "guid": "bfdfe7dc352907fc980b868725387e983bddbb326feaacd5f05624640d2c8220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98127603f54767a6ed3b64efd832839124", "guid": "bfdfe7dc352907fc980b868725387e989bfb82f791b09bee79e5a1fce863374c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99f508d516ab4b6a559d0e6d5b325ed", "guid": "bfdfe7dc352907fc980b868725387e98d28d8aa001c23469489a805d4c9480f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98185d19dadb8fc36265639020e0538ec9", "guid": "bfdfe7dc352907fc980b868725387e987c9bca1cc6a02725d93cd8dcad1d39eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad4167ba32ec93d72fc1cf2cb09c3405", "guid": "bfdfe7dc352907fc980b868725387e981a6455fd28da6499835daab5f49eb537"}], "guid": "bfdfe7dc352907fc980b868725387e9899d5bb82be8b19fca0eedde21c631a4e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e98afa14c9d9f3e5e90e2ba491821afc5d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bad4511123a4308156839eb8661b604", "guid": "bfdfe7dc352907fc980b868725387e988895e84806fe496c5f65cbfd42c764ba"}], "guid": "bfdfe7dc352907fc980b868725387e989c0e1cc40a9720c837aacc3346ee1fcc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984a2c29a10382e2249f3872635255efa0", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e989bead5a4a9cd6b1c33f2ff52c50ab311", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}