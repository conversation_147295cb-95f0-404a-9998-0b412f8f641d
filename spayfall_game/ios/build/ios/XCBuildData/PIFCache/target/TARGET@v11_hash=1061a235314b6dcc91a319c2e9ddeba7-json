{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1876edfd40d23f201d8273bd548d540", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e66fdd7e62c48c5dbd0217cd0e68028a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c40f1a0dc6e821f28d2ae9672425e2b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9840f6da526ff88eec3fcae6279cc76b71", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c40f1a0dc6e821f28d2ae9672425e2b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986de56db7a08ea4a4f44950fd1518e2c6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986259704b4fe5e2a582fe74f800b2b04b", "guid": "bfdfe7dc352907fc980b868725387e98f3095d5600fac1d93a09597c1ad62e99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98434292aaa67aa5ecafe1d33f95b63c34", "guid": "bfdfe7dc352907fc980b868725387e98fd5a91dd0a408206908f2e86bd474303", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cddea577cd9de563aeb535952b07f0cd", "guid": "bfdfe7dc352907fc980b868725387e98c92317de24a1137fa1cd20edf999eb73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883484194313ab6c4b5943a839de56628", "guid": "bfdfe7dc352907fc980b868725387e986f2d4247ff82755d5b25928f213ae8c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98499d6208ec701ec163fb2f08b8b86cf1", "guid": "bfdfe7dc352907fc980b868725387e9858f83349872f31ea6bc15cf81f11ab9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efe54af1b8353d6bd9959cac80fa80a3", "guid": "bfdfe7dc352907fc980b868725387e98b697472e012fe695e915cd533537998e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98066d10a1ec7d66a483fb5eed9957995c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7adb3e174e5769928652467a32e8823", "guid": "bfdfe7dc352907fc980b868725387e98032fd85ffb359c79df2d857ffc919866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886037de1d35164da1a84c47b755f06d0", "guid": "bfdfe7dc352907fc980b868725387e98cd1d5ac219bf625d38df14c813ed1438"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c9fb7d248e59f50f4745c022db1aece", "guid": "bfdfe7dc352907fc980b868725387e98faddb3049d4c53c36e699f19a5983939"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98110fc813a8ae4deb0ca41f4beb1abb6e", "guid": "bfdfe7dc352907fc980b868725387e9839699c567db68558e613493c467bf8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e09e8ccf0849f0a8d080fead8c8aee4a", "guid": "bfdfe7dc352907fc980b868725387e98d989864eecb32473464e06107009e046"}], "guid": "bfdfe7dc352907fc980b868725387e984e18bb278449306d78edf30f903919b6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e98e8b7121ec75a064f6626793bb25dcc47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bad4511123a4308156839eb8661b604", "guid": "bfdfe7dc352907fc980b868725387e98c8b883bddd203a83f3e274625464319b"}], "guid": "bfdfe7dc352907fc980b868725387e98f5e6eea6e27917a3457210360ed9a830", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988238f0f02e121c3da3ba72a2b0e47a6e", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e9891fb3704ac06dcc8ed70e17d4c7ed479", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}