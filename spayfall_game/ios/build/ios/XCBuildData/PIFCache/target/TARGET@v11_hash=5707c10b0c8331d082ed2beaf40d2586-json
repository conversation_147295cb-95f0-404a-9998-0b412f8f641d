{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9865348cd80abef9185c99d2c38a33da88", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d89bd4a5103f5d74bbeab7294dba137", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852196036d02e6386e121b1ec12e31bc2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fcb5cdb976af1ba15c9a17c5f07d17a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852196036d02e6386e121b1ec12e31bc2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9893a77c8d07cf619c15087cce71e87088", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9820a1c67b4b01289c7ccf3c0a7cc8d8d3", "guid": "bfdfe7dc352907fc980b868725387e989fd6bc486608dacb6d45ab75af14f610", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbca881f39ee9968a021c224bc56dd1c", "guid": "bfdfe7dc352907fc980b868725387e986c90165ae430b27ec82af356685832f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab6531b9cbca3c41caa8df33a121b36a", "guid": "bfdfe7dc352907fc980b868725387e98852ed0bde165d32ab33a32f11b9d102b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815192df6ec0abdd8eb64037cd80fbe0c", "guid": "bfdfe7dc352907fc980b868725387e98aaa7a5c106a0c36ad877a37f1a3604b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98285ba8fde2b0db9d4c8058a2094fa8b8", "guid": "bfdfe7dc352907fc980b868725387e980d3152f1b72084b514a9810d1dc3f2c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7912f4011f32724109451a4a88f8580", "guid": "bfdfe7dc352907fc980b868725387e98d6d1ec4b63d414c9fb6aab4aac285a08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9f0856c825473612b9fb1a5870ef2b8", "guid": "bfdfe7dc352907fc980b868725387e9854aa64b89997884080f265eecb363d8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c04cd8bac5f940db0d712d951f478fc4", "guid": "bfdfe7dc352907fc980b868725387e98a2085365c39cf349f6d1ab7a6398f1ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980845ed17fbb93695bbd5cf775872b8a0", "guid": "bfdfe7dc352907fc980b868725387e9845c2959607d59a92505d249af59f0f39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984207fa1528b7fc2c11872ca3d8b5dbeb", "guid": "bfdfe7dc352907fc980b868725387e98d4c602e993ddff6aa51a0cbfb6bcb037", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824b2632c238185258be249e728cd7a97", "guid": "bfdfe7dc352907fc980b868725387e985e77cd148bfe1a013742a0f75741b4e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5806229e0d2ad2d7e00e5046a186b81", "guid": "bfdfe7dc352907fc980b868725387e98a1233c945fee3e9b231c1496da193737", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989769da3443f2d902ab4bf62e14363ed1", "guid": "bfdfe7dc352907fc980b868725387e98af873f7287e5347493df8c9f32c66dae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b7db648a48d664be771ac719d98c1c9", "guid": "bfdfe7dc352907fc980b868725387e980332cecd238f8a0f507d9ea4cbec19de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867212282f2efe9fec6bb6897edddba25", "guid": "bfdfe7dc352907fc980b868725387e98c58679835cef877cc020a49a7d09ac7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98547a1c12a40a72035a586a9d2a99902b", "guid": "bfdfe7dc352907fc980b868725387e98c9ac915f1f1a0b33ddd8ace0dfcce45d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d706cf6d85379c6ff571913abedda58f", "guid": "bfdfe7dc352907fc980b868725387e98ca134a8b9aeec7c5d7ee7f177b63a12c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989db2e32472c1451ab2a58e9c46751892", "guid": "bfdfe7dc352907fc980b868725387e984f35c144ffab9256d2e71cdbfd78beef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b010b901e7c28bdccee6ca4c966a9ff6", "guid": "bfdfe7dc352907fc980b868725387e98fc12d63c4e3f23d532e518db45df1b28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984feae49619aaa56044224b4f14d7f8d4", "guid": "bfdfe7dc352907fc980b868725387e983f5c2de737f3f000b49ec58ce15b2980", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeb4fa40aac7d9128cf82d4d62266a74", "guid": "bfdfe7dc352907fc980b868725387e983701eff0cd4d0ededd5c7347a839af57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a8dce2b4dc7c10e4d135c9c716ccc98", "guid": "bfdfe7dc352907fc980b868725387e98ff3b7c0a34a35b47a2f37b60ec739c52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a025956079ff1e679b6811012ab0a5", "guid": "bfdfe7dc352907fc980b868725387e98bf411b77c85d01d1ccebc33f467ca4fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f44adf8b251dd084dfc6a140e81fd0", "guid": "bfdfe7dc352907fc980b868725387e98e251b4e2e7754459068327d65c56c493", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988251a5a894f3d7d8fe8bed76b8235590", "guid": "bfdfe7dc352907fc980b868725387e982978378b13c4dcc135b5b0c3de1ed244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d8d790dd955c273423aabb03858eea", "guid": "bfdfe7dc352907fc980b868725387e984eef7943012e5bb3d0887f1434d6db0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805f989ad717b0671ca27a118d6bfdbf1", "guid": "bfdfe7dc352907fc980b868725387e988f41937c43b7886a060e1fdb89edfb82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834bf500ffb83843b01f80a0efcfc1163", "guid": "bfdfe7dc352907fc980b868725387e980168bf8452e54bcbea93b419a982fa4c"}], "guid": "bfdfe7dc352907fc980b868725387e98f6cece53b96413ac71474ff6643c1a77", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98884eda38e2b3cc233ff90106b20c808b", "guid": "bfdfe7dc352907fc980b868725387e9875c2350071b967064fb93c7a150a8279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caa462e578527a61674c39bceb167559", "guid": "bfdfe7dc352907fc980b868725387e98516714268c1ccfec0e7ba9003609542f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b3d47c3dcdfce9e88c9341db8e90c8b", "guid": "bfdfe7dc352907fc980b868725387e98c1e38d06d6de2bc7712d72ff6e068c9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f7fd5660fadb65402189efb91e839b", "guid": "bfdfe7dc352907fc980b868725387e98430f82b2f45d6a987607e2f22f8bd97d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d07d9809e0d59dc5acf35774ff5e4676", "guid": "bfdfe7dc352907fc980b868725387e98b61bc8231503e43a8b74edcf34f42633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d57ba1a264fc4b86529202dd0d7533", "guid": "bfdfe7dc352907fc980b868725387e98dca1fb90ac15cd7e37e074961b6bf32b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98093b163ed5e86e68005ada57777b0119", "guid": "bfdfe7dc352907fc980b868725387e9873c686259dc2986f2b830ed320ef9887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7957540e294385abb29feb93ec27512", "guid": "bfdfe7dc352907fc980b868725387e98b5eed0d9b507bffa74bd72989b2615b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce1ed663364ed73d22ebbc410b9f8183", "guid": "bfdfe7dc352907fc980b868725387e980a979c9bda5fc8ea3c179de34330d6b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb25c2fa648951e67c5a495b7c6537b", "guid": "bfdfe7dc352907fc980b868725387e98a8a551c0f0e7d06b6618cfcbed874d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e35f86150bb2269ddf4215ef59ce1a0", "guid": "bfdfe7dc352907fc980b868725387e986d695bd8db9224875b437366dc471104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e33981b7ca439b32d32d466f7342ec2", "guid": "bfdfe7dc352907fc980b868725387e980d32300516021dab649b563a8ae37d28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c749e9463a8389ee904bf98458a87e3e", "guid": "bfdfe7dc352907fc980b868725387e982a24f6d49d1d30f2985d5b040b51737c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b2576ede3d496bc6893c3197411fdc8", "guid": "bfdfe7dc352907fc980b868725387e988ee19157f3ada4b4cac9d43df635ce55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834a7d98e46ac072ecd00db139162b6a3", "guid": "bfdfe7dc352907fc980b868725387e98b9d6a89197904046e33f6a57eba0b207"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f34950c86621222adc90bd7a16b3bc7", "guid": "bfdfe7dc352907fc980b868725387e98f657bcaaa87772b99858a6b5a850156d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987889e74e65f94bb339652aaaf5b21416", "guid": "bfdfe7dc352907fc980b868725387e988f0c64cdea6123bc4bad230f7357723e"}], "guid": "bfdfe7dc352907fc980b868725387e98813a3050161ca6b8990331c7478c1c6a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863c2e7d7781acd23b9bc2919c54c06e4", "guid": "bfdfe7dc352907fc980b868725387e98088d8b27d62fdf878379876429d15f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bad4511123a4308156839eb8661b604", "guid": "bfdfe7dc352907fc980b868725387e98f45ebf52715c0dc37b129810c4cbe801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2052c6ceef90a4adafb571425208587", "guid": "bfdfe7dc352907fc980b868725387e98be0a769bdf80f5f0f5529354f1a1deb5"}], "guid": "bfdfe7dc352907fc980b868725387e98ebb50f0aca83b2a247ec96e1e6045498", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983e4a250956ec5ed440fc6176f2170a0b", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98c08353cc4fb6ce230dd760a4e74bd31a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}